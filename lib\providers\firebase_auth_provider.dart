import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/firebase_service.dart';

class FirebaseAuthProvider extends ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  User? _user;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _user != null;

  FirebaseAuthProvider() {
    // Écouter les changements d'état d'authentification
    _auth.authStateChanges().listen((User? user) {
      _user = user;
      notifyListeners();
    });
  }

  /// Connexion avec email et mot de passe
  Future<bool> signInWithEmailAndPassword(String email, String password) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      print('🔥 Début de la connexion pour: $email');

      final UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      _user = result.user;
      print('🔥 Utilisateur connecté: ${_user?.uid}');

      // Vérifier le statut de l'utilisateur
      if (_user != null) {
        print('🔥 Vérification du statut utilisateur...');
        final userStatus = await _getUserStatus(_user!.uid);
        print('🔥 Statut utilisateur: $userStatus');
        
        if (userStatus != 'actif') {
          print('🔥 Compte inactif, déconnexion...');
          await _auth.signOut();
          _user = null;
          _errorMessage = 'Votre compte n\'est pas encore activé. Veuillez contacter un administrateur.';
          _isLoading = false;
          notifyListeners();
          return false;
        }
        
        // Mettre à jour la dernière connexion
        print('🔥 Mise à jour de la dernière connexion...');
        await _updateLastLogin(_user!.uid);
      }

      print('🔥 Connexion réussie!');
      _isLoading = false;
      notifyListeners();
      return true;
    } on FirebaseAuthException catch (e) {
      print('🔥 Erreur Firebase Auth: ${e.code} - ${e.message}');
      _isLoading = false;
      _errorMessage = _getErrorMessage(e.code);
      notifyListeners();
      return false;
    } catch (e) {
      print('🔥 Erreur inattendue lors de la connexion: $e');
      _isLoading = false;
      _errorMessage = 'Une erreur inattendue s\'est produite: $e';
      notifyListeners();
      return false;
    }
  }

  /// Inscription avec email et mot de passe
  Future<bool> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String nomComplet,
    required String telephone,
    String? mobile,
    String? territoire,
    required String userType,
  }) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      _user = result.user;

      // Créer le profil utilisateur dans la collection appropriée
      if (_user != null) {
        await _createUserProfile(
          uid: _user!.uid,
          email: email,
          nomComplet: nomComplet,
          telephone: telephone,
          mobile: mobile,
          territoire: territoire,
          userType: userType,
        );
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } on FirebaseAuthException catch (e) {
      _isLoading = false;
      _errorMessage = _getErrorMessage(e.code);
      notifyListeners();
      return false;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Une erreur inattendue s\'est produite';
      notifyListeners();
      return false;
    }
  }

  /// Inscription avec données complètes pour commercial ou merchandiser
  Future<bool> signUpWithUserData({
    required String email,
    required String password,
    required String nomComplet,
    required String telephone,
    required String userType, // 'commercial' ou 'merchandiser'
    String? mobile,
    String? territoire,
    String status = 'Inactif',
  }) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      print('🔥 Début de l\'inscription pour: $email, type: $userType');

      // Créer l'utilisateur avec Firebase Auth
      final UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      _user = result.user;
      print('🔥 Utilisateur Firebase Auth créé avec UID: ${_user?.uid}');

      // Créer le profil utilisateur dans Firestore
      if (_user != null) {
        print('🔥 Création du profil utilisateur dans Firestore...');
        await _createCompleteUserProfile(
          uid: _user!.uid,
          email: email,
          nomComplet: nomComplet,
          telephone: telephone,
          userType: userType,
          mobile: mobile,
          territoire: territoire,
          status: status,
        );
        print('🔥 Profil utilisateur créé avec succès!');
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } on FirebaseAuthException catch (e) {
      _isLoading = false;
      _errorMessage = _getErrorMessage(e.code);
      print('🔥 Erreur Firebase Auth: ${e.code} - ${e.message}');
      notifyListeners();
      return false;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Une erreur inattendue s\'est produite: $e';
      print('🔥 Erreur inattendue: $e');
      notifyListeners();
      return false;
    }
  }

  /// Déconnexion
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      _user = null;
      _errorMessage = null;
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Erreur lors de la déconnexion';
      notifyListeners();
    }
  }

  /// Réinitialiser le mot de passe
  Future<bool> resetPassword(String email) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await _auth.sendPasswordResetEmail(email: email);

      _isLoading = false;
      notifyListeners();
      return true;
    } on FirebaseAuthException catch (e) {
      _isLoading = false;
      _errorMessage = _getErrorMessage(e.code);
      notifyListeners();
      return false;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Une erreur inattendue s\'est produite';
      notifyListeners();
      return false;
    }
  }

  /// Obtenir le profil utilisateur depuis Firestore
  Future<Map<String, dynamic>?> getUserProfile() async {
    if (_user == null) return null;

    try {
      final doc = await FirebaseService.users.doc(_user!.uid).get();
      return doc.data() as Map<String, dynamic>?;
    } catch (e) {
      print('Erreur lors de la récupération du profil: $e');
      return null;
    }
  }

  /// Mettre à jour le profil utilisateur
  Future<bool> updateUserProfile(Map<String, dynamic> data) async {
    if (_user == null) return false;

    try {
      await FirebaseService.users.doc(_user!.uid).update(data);
      return true;
    } catch (e) {
      _errorMessage = 'Erreur lors de la mise à jour du profil';
      notifyListeners();
      return false;
    }
  }

  /// Créer le profil utilisateur dans Firestore
  Future<void> _createUserProfile({
    required String uid,
    required String email,
    required String nomComplet,
    required String telephone,
    String? mobile,
    String? territoire,
    required String userType,
  }) async {
    // Données communes
    final userData = {
      'uid': uid,
      'email': email,
      'nomComplet': nomComplet,
      'telephone': telephone,
      'mobile': mobile ?? '',
      'territoire': territoire ?? '',
      'statut': 'inactif', // Par défaut inactif
      'createdAt': FieldValue.serverTimestamp(),
      'lastLogin': null,
    };

    // Sauvegarder dans la collection appropriée
    if (userType == 'commercial') {
      await FirebaseService.firestore.collection('commercial').doc(uid).set(userData);
    } else if (userType == 'merchandiser') {
      await FirebaseService.firestore.collection('merchandiser').doc(uid).set(userData);
    }

    // Également sauvegarder dans la collection users pour compatibilité
    await FirebaseService.users.doc(uid).set({
      'email': email,
      'nomComplet': nomComplet,
      'userType': userType,
      'statut': 'inactif',
      'createdAt': FieldValue.serverTimestamp(),
      'lastLogin': null,
    });
  }

  /// Créer le profil utilisateur complet avec données spécifiques au type
  Future<void> _createCompleteUserProfile({
    required String uid,
    required String email,
    required String nomComplet,
    required String telephone,
    required String userType,
    String? mobile,
    String? territoire,
    String status = 'Inactif',
  }) async {
    try {
      print(
        '🔥 _createCompleteUserProfile appelée avec UID: $uid, type: $userType',
      );

      // Données communes pour tous les utilisateurs
      final commonData = {
        'email': email,
        'nomComplet': nomComplet,
        'userType': userType,
        'telephone': telephone,
        'mobile': mobile,
        'territoire': territoire,
        'status': status,
        'createdAt': FieldValue.serverTimestamp(),
        'lastLogin': null,
      };

      print('🔥 Sauvegarde dans la collection users...');
      // Sauvegarder dans la collection users
      await FirebaseService.users.doc(uid).set(commonData);
      print('🔥 Sauvegarde dans users réussie!');

      // Sauvegarder dans la collection spécifique selon le type d'utilisateur
      if (userType == 'commercial') {
        print('🔥 Sauvegarde dans la collection commercial...');
        await FirebaseService.commercial.doc(uid).set({
          'userId': uid,
          'email': email,
          'nomComplet': nomComplet,
          'telephone': telephone,
          'mobile': mobile,
          'territoire': territoire,
          'status': status,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
        print('🔥 Sauvegarde dans commercial réussie!');
      } else if (userType == 'merchandiser') {
        print('🔥 Sauvegarde dans la collection merchandiser...');
        await FirebaseService.merchandiser.doc(uid).set({
          'userId': uid,
          'email': email,
          'nomComplet': nomComplet,
          'telephone': telephone,
          'mobile': mobile,
          'zone': territoire, // Pour les merchandisers, territoire devient zone
          'status': status,
          'actif': status == 'actif' ? true : false,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
        print('🔥 Sauvegarde dans merchandiser réussie!');
      }
    } catch (e) {
      print('🔥 Erreur lors de la création du profil: $e');
      rethrow;
    }
  }

  /// Mettre à jour la dernière connexion
  Future<void> _updateLastLogin(String uid) async {
    try {
      // Obtenir le type d'utilisateur d'abord
      final userDoc = await FirebaseService.users.doc(uid).get();
      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        final userType = userData['userType'] as String?;
        
        // Mettre à jour dans la collection spécifique
        if (userType == 'commercial') {
          await FirebaseService.firestore.collection('commercial').doc(uid).update({
            'lastLogin': FieldValue.serverTimestamp(),
          });
        } else if (userType == 'merchandiser') {
          await FirebaseService.firestore.collection('merchandiser').doc(uid).update({
            'lastLogin': FieldValue.serverTimestamp(),
          });
        }
        
        // Mettre à jour aussi dans users
        await FirebaseService.users.doc(uid).update({
          'lastLogin': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      print('Erreur lors de la mise à jour de lastLogin: $e');
    }
  }

  /// Vérifier le statut de l'utilisateur
  Future<String> _getUserStatus(String uid) async {
    try {
      // Vérifier d'abord dans la collection users pour obtenir le type
      final userDoc = await FirebaseService.users.doc(uid).get();
      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        final userType = userData['userType'] as String?;
        
        // Vérifier le statut dans la collection spécifique
        if (userType == 'commercial') {
          final commercialDoc = await FirebaseService.firestore.collection('commercial').doc(uid).get();
          if (commercialDoc.exists) {
            final data = commercialDoc.data() as Map<String, dynamic>;
            return data['status'] as String? ?? 'inactif';
          }
        } else if (userType == 'merchandiser') {
          final merchandiserDoc = await FirebaseService.firestore.collection('merchandiser').doc(uid).get();
          if (merchandiserDoc.exists) {
            final data = merchandiserDoc.data() as Map<String, dynamic>;
            return data['status'] as String? ?? 'inactif';
          }
        }
        
        // Fallback: vérifier dans users
        return userData['status'] as String? ?? 'inactif';
      }
      
      return 'inactif';
    } catch (e) {
      print('Erreur lors de la vérification du statut: $e');
      return 'inactif';
    }
  }

  /// Obtenir le message d'erreur en français
  String _getErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'user-not-found':
        return 'Aucun utilisateur trouvé avec cet email';
      case 'wrong-password':
        return 'Mot de passe incorrect';
      case 'email-already-in-use':
        return 'Cet email est déjà utilisé';
      case 'weak-password':
        return 'Le mot de passe est trop faible';
      case 'invalid-email':
        return 'Email invalide';
      case 'user-disabled':
        return 'Ce compte utilisateur a été désactivé';
      case 'too-many-requests':
        return 'Trop de tentatives, réessayez plus tard';
      default:
        return 'Erreur d\'authentification';
    }
  }

  /// Effacer le message d'erreur
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
