import 'package:cloud_firestore/cloud_firestore.dart';
import 'firebase_service.dart';

class AdminService {
  /// Obtenir tous les utilisateurs commerciaux avec leur statut
  static Future<List<Map<String, dynamic>>> getAllCommercialUsers() async {
    try {
      final querySnapshot = await FirebaseService.commercial.get();
      return querySnapshot.docs
          .map((doc) => {
                'uid': doc.id,
                ...doc.data() as Map<String, dynamic>,
              })
          .toList();
    } catch (e) {
      print('Erreur lors de la récupération des commerciaux: $e');
      return [];
    }
  }

  /// Obtenir tous les utilisateurs merchandisers avec leur statut
  static Future<List<Map<String, dynamic>>> getAllMerchandiserUsers() async {
    try {
      final querySnapshot = await FirebaseService.merchandiser.get();
      return querySnapshot.docs
          .map((doc) => {
                'uid': doc.id,
                ...doc.data() as Map<String, dynamic>,
              })
          .toList();
    } catch (e) {
      print('Erreur lors de la récupération des merchandisers: $e');
      return [];
    }
  }

  /// Changer le statut d'un utilisateur commercial
  static Future<bool> updateCommercialStatus(String uid, String newStatus) async {
    try {
      await FirebaseService.commercial.doc(uid).update({
        'status': newStatus,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Mettre à jour aussi dans la collection users pour cohérence
      await FirebaseService.users.doc(uid).update({
        'status': newStatus,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Erreur lors de la mise à jour du statut commercial: $e');
      return false;
    }
  }

  /// Changer le statut d'un utilisateur merchandiser
  static Future<bool> updateMerchandiserStatus(String uid, String newStatus) async {
    try {
      await FirebaseService.merchandiser.doc(uid).update({
        'status': newStatus,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Mettre à jour aussi dans la collection users pour cohérence
      await FirebaseService.users.doc(uid).update({
        'status': newStatus,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Erreur lors de la mise à jour du statut merchandiser: $e');
      return false;
    }
  }

  /// Obtenir tous les utilisateurs (commercial + merchandiser) en attente d'activation
  static Future<List<Map<String, dynamic>>> getPendingUsers() async {
    try {
      final commercialUsers = await getAllCommercialUsers();
      final merchandiserUsers = await getAllMerchandiserUsers();

      final pendingUsers = <Map<String, dynamic>>[];

      // Filtrer les utilisateurs inactifs
      for (final user in commercialUsers) {
        if (user['status'] == 'inactif') {
          user['userType'] = 'commercial';
          pendingUsers.add(user);
        }
      }

      for (final user in merchandiserUsers) {
        if (user['status'] == 'inactif') {
          user['userType'] = 'merchandiser';
          pendingUsers.add(user);
        }
      }

      // Trier par date de création (plus récent en premier)
      pendingUsers.sort((a, b) {
        final aDate = a['createdAt'] as Timestamp?;
        final bDate = b['createdAt'] as Timestamp?;
        if (aDate == null || bDate == null) return 0;
        return bDate.compareTo(aDate);
      });

      return pendingUsers;
    } catch (e) {
      print('Erreur lors de la récupération des utilisateurs en attente: $e');
      return [];
    }
  }

  /// Activer un utilisateur (changer son statut à 'actif')
  static Future<bool> activateUser(String uid, String userType) async {
    if (userType == 'commercial') {
      return await updateCommercialStatus(uid, 'actif');
    } else if (userType == 'merchandiser') {
      return await updateMerchandiserStatus(uid, 'actif');
    }
    return false;
  }

  /// Désactiver un utilisateur (changer son statut à 'inactif')
  static Future<bool> deactivateUser(String uid, String userType) async {
    if (userType == 'commercial') {
      return await updateCommercialStatus(uid, 'inactif');
    } else if (userType == 'merchandiser') {
      return await updateMerchandiserStatus(uid, 'inactif');
    }
    return false;
  }

  /// Supprimer complètement un utilisateur (à utiliser avec précaution)
  static Future<bool> deleteUser(String uid, String userType) async {
    try {
      // Supprimer de la collection spécifique
      if (userType == 'commercial') {
        await FirebaseService.commercial.doc(uid).delete();
      } else if (userType == 'merchandiser') {
        await FirebaseService.merchandiser.doc(uid).delete();
      }

      // Supprimer aussi de la collection users
      await FirebaseService.users.doc(uid).delete();

      // Note: Pour supprimer complètement l'utilisateur de Firebase Auth,
      // vous devrez utiliser les Admin SDKs côté serveur
      // ou demander à l'utilisateur de supprimer son propre compte

      return true;
    } catch (e) {
      print('Erreur lors de la suppression de l\'utilisateur: $e');
      return false;
    }
  }
}
