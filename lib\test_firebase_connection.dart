import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'services/firebase_service.dart';

class TestFirebaseConnection extends StatefulWidget {
  @override
  _TestFirebaseConnectionState createState() => _TestFirebaseConnectionState();
}

class _TestFirebaseConnectionState extends State<TestFirebaseConnection> {
  String _status = 'Prêt à tester';
  bool _isLoading = false;

  Future<void> _testFirebaseConnection() async {
    setState(() {
      _isLoading = true;
      _status = 'Test en cours...';
    });

    try {
      // Test 1: Vérifier la connexion Firestore
      print('🔥 Test 1: Connexion Firestore...');
      await FirebaseFirestore.instance.collection('test').doc('test').set({
        'message': 'Test de connexion',
        'timestamp': FieldValue.serverTimestamp(),
      });
      print('✅ Test 1 réussi: Firestore connecté');

      // Test 2: Créer un utilisateur de test
      print('🔥 Test 2: Création utilisateur Firebase Auth...');
      final testEmail = 'test${DateTime.now().millisecondsSinceEpoch}@example.com';
      UserCredential? result;
      try {
        result = await FirebaseAuth.instance.createUserWithEmailAndPassword(
          email: testEmail,
          password: 'password123',
        );
        print('✅ Test 2 réussi: Utilisateur créé avec UID: ${result.user?.uid}');
      } catch (authError) {
        print('❌ Erreur création utilisateur: $authError');
        throw authError;
      }

      // Test 3: Sauvegarder dans la collection users
      if (result.user != null) {
        print('🔥 Test 3: Sauvegarde dans collection users...');
        await FirebaseService.users.doc(result.user!.uid).set({
          'email': testEmail,
          'nom': 'Test User',
          'role': 'commercial',
          'telephone': '0123456789',
          'status': 'Inactif',
          'createdAt': FieldValue.serverTimestamp(),
        });
        print('✅ Test 3 réussi: Données sauvées dans users');

        // Test 4: Sauvegarder dans la collection commercial
        print('🔥 Test 4: Sauvegarde dans collection commercial...');
        await FirebaseService.commercial.doc(result.user!.uid).set({
          'userId': result.user!.uid,
          'email': testEmail,
          'nom': 'Test User',
          'telephone': '0123456789',
          'mobile': '0987654321',
          'territoire': 'Test Territory',
          'status': 'inactif',
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
        print('✅ Test 4 réussi: Données sauvées dans commercial');

        // Test 5: Test collection merchandiser aussi
        print('🔥 Test 5: Test collection merchandiser...');
        await FirebaseService.merchandiser.doc('test-merchandiser').set({
          'userId': 'test-merchandiser-uid',
          'email': '<EMAIL>',
          'nom': 'Test Merchandiser',
          'telephone': '0123456789',
          'mobile': '0987654321',
          'zone': 'Test Zone',
          'status': 'inactif',
          'actif': false,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
        print('✅ Test 5 réussi: Collection merchandiser testée');

        // Test 6: Nettoyer - supprimer l'utilisateur de test
        print('🔥 Test 6: Nettoyage...');
        try {
          // Supprimer les documents Firestore d'abord
          await FirebaseService.users.doc(result.user!.uid).delete();
          await FirebaseService.commercial.doc(result.user!.uid).delete();
          await FirebaseService.merchandiser.doc('test-merchandiser').delete();
          print('✅ Documents Firestore supprimés');
          
          // Ensuite supprimer l'utilisateur Firebase Auth
          await result.user!.delete();
          print('✅ Utilisateur Firebase Auth supprimé');
        } catch (deleteError) {
          print('⚠️ Erreur lors du nettoyage (non critique): $deleteError');
        }
        print('✅ Test 6 réussi: Nettoyage terminé');
      }

      setState(() {
        _status = '✅ Tous les tests réussis!\n\n• Firebase Auth ✓\n• Collection users ✓\n• Collection commercial ✓\n• Collection merchandiser ✓\n\nSystème d\'authentification prêt!';
        _isLoading = false;
      });

    } catch (e) {
      print('❌ Erreur lors du test: $e');
      setState(() {
        _status = '❌ Erreur: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Test Firebase Connection'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _status.startsWith('✅') ? Icons.check_circle : 
              _status.startsWith('❌') ? Icons.error : Icons.info,
              size: 64,
              color: _status.startsWith('✅') ? Colors.green : 
                     _status.startsWith('❌') ? Colors.red : Colors.blue,
            ),
            SizedBox(height: 20),
            Text(
              _status,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 40),
            ElevatedButton(
              onPressed: _isLoading ? null : _testFirebaseConnection,
              child: _isLoading 
                ? CircularProgressIndicator(color: Colors.white)
                : Text('Tester Firebase'),
            ),
          ],
        ),
      ),
    );
  }
}
